import { Transaction, TransactionItem, TransactionType, TransactionStatus, TransactionSource, PaymentMethod } from './transaction-types';

/**
 * Service for creating consolidated transactions that combine services and products
 * into a single transaction record per payment session
 */
export class ConsolidatedTransactionService {
  
  /**
   * Generate a unique transaction ID for a payment session
   */
  static generateTransactionId(): string {
    const timestamp = Date.now();
    const randomSuffix = Math.random().toString(36).substring(2, 8);
    return `TX-CONS-${timestamp}-${randomSuffix}`;
  }

  /**
   * Create a consolidated transaction from appointment data
   */
  static createConsolidatedTransaction(
    appointment: any,
    paymentMethod: PaymentMethod,
    discountPercentage?: number,
    discountAmount?: number
  ): Transaction {
    const transactionId = this.generateTransactionId();
    const items: TransactionItem[] = [];
    let serviceAmount = 0;
    let productAmount = 0;
    let originalServiceAmount = 0;

    // Process main service
    if (appointment.service && appointment.price) {
      const serviceItem: TransactionItem = {
        id: `service-${appointment.service.toLowerCase().replace(/\s+/g, '-')}`,
        name: appointment.service,
        quantity: 1,
        unitPrice: appointment.price,
        totalPrice: appointment.price,
        type: 'service',
        discountApplied: false,
        discountPercentage: 0,
        discountAmount: 0,
        originalPrice: appointment.price
      };

      // Apply discount to service if provided
      if (discountPercentage && discountPercentage > 0) {
        const itemDiscountAmount = (appointment.price * discountPercentage) / 100;
        serviceItem.discountApplied = true;
        serviceItem.discountPercentage = discountPercentage;
        serviceItem.discountAmount = itemDiscountAmount;
        serviceItem.totalPrice = appointment.price - itemDiscountAmount;
      }

      items.push(serviceItem);
      originalServiceAmount += appointment.price;
      serviceAmount += serviceItem.totalPrice;
    }

    // Process additional services
    if (appointment.additionalServices && appointment.additionalServices.length > 0) {
      appointment.additionalServices.forEach((service: any) => {
        const serviceItem: TransactionItem = {
          id: `service-${service.name.toLowerCase().replace(/\s+/g, '-')}`,
          name: service.name,
          quantity: 1,
          unitPrice: service.price,
          totalPrice: service.price,
          type: 'service',
          discountApplied: false,
          discountPercentage: 0,
          discountAmount: 0,
          originalPrice: service.price
        };

        // Apply discount to additional services if provided
        if (discountPercentage && discountPercentage > 0) {
          const itemDiscountAmount = (service.price * discountPercentage) / 100;
          serviceItem.discountApplied = true;
          serviceItem.discountPercentage = discountPercentage;
          serviceItem.discountAmount = itemDiscountAmount;
          serviceItem.totalPrice = service.price - itemDiscountAmount;
        }

        items.push(serviceItem);
        originalServiceAmount += service.price;
        serviceAmount += serviceItem.totalPrice;
      });
    }

    // Process products (no discount applied)
    if (appointment.products && appointment.products.length > 0) {
      appointment.products.forEach((product: any) => {
        const productItem: TransactionItem = {
          id: product.id || `product-${product.name.toLowerCase().replace(/\s+/g, '-')}`,
          name: product.name,
          quantity: product.quantity || 1,
          unitPrice: product.price,
          totalPrice: (product.price * (product.quantity || 1)),
          type: 'product',
          discountApplied: false, // Products never have discounts applied
          cost: product.cost
        };

        items.push(productItem);
        productAmount += productItem.totalPrice;
      });
    }

    // Calculate total amount
    const totalAmount = serviceAmount + productAmount;

    // Determine transaction type
    const transactionType = items.some(item => item.type === 'service') && items.some(item => item.type === 'product')
      ? TransactionType.CONSOLIDATED_SALE
      : items.some(item => item.type === 'service')
      ? TransactionType.SERVICE_SALE
      : TransactionType.PRODUCT_SALE;

    // Create description
    const serviceCount = items.filter(item => item.type === 'service').length;
    const productCount = items.filter(item => item.type === 'product').length;
    let description = '';
    
    if (serviceCount > 0 && productCount > 0) {
      description = `Appointment payment - ${serviceCount} service(s) and ${productCount} product(s)`;
    } else if (serviceCount > 0) {
      description = `Appointment payment - ${appointment.service}${appointment.additionalServices?.length ? ` + ${appointment.additionalServices.length} additional service(s)` : ''}`;
    } else {
      description = `Appointment payment - ${productCount} product(s)`;
    }

    // Add discount information to description
    if (discountPercentage && discountPercentage > 0) {
      description += ` (${discountPercentage}% discount applied to services)`;
    }

    const transaction: Transaction = {
      id: transactionId,
      date: new Date(),
      clientId: appointment.clientId,
      clientName: appointment.clientName,
      staffId: appointment.staffId,
      staffName: appointment.staffName,
      type: transactionType,
      category: transactionType === TransactionType.CONSOLIDATED_SALE ? "Consolidated Sale" : 
                transactionType === TransactionType.SERVICE_SALE ? "Service Sale" : "Product Sale",
      description,
      amount: totalAmount,
      paymentMethod,
      status: TransactionStatus.COMPLETED,
      location: appointment.location || "loc1",
      source: TransactionSource.CALENDAR,
      reference: {
        type: "appointment",
        id: appointment.id
      },
      items,
      serviceAmount,
      productAmount,
      originalServiceAmount,
      discountPercentage,
      discountAmount,
      metadata: {
        appointmentId: appointment.id,
        transactionType: 'consolidated',
        serviceCount,
        productCount,
        discountApplied: discountPercentage && discountPercentage > 0,
        originalTotal: originalServiceAmount + productAmount,
        finalTotal: totalAmount
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    return transaction;
  }

  /**
   * Create a consolidated transaction from booking summary data
   */
  static createConsolidatedTransactionFromBooking(
    booking: any,
    paymentMethod: PaymentMethod,
    discountPercentage?: number,
    discountAmount?: number
  ): Transaction {
    const transactionId = this.generateTransactionId();
    const items: TransactionItem[] = [];
    let serviceAmount = 0;
    let productAmount = 0;
    let originalServiceAmount = 0;

    // Process booking items
    if (booking.items && booking.items.length > 0) {
      booking.items.forEach((item: any) => {
        const transactionItem: TransactionItem = {
          id: item.id || `item-${item.name.toLowerCase().replace(/\s+/g, '-')}`,
          name: item.name,
          quantity: 1,
          unitPrice: item.price,
          totalPrice: item.price,
          type: item.type || 'service', // Default to service if type not specified
          discountApplied: false,
          discountPercentage: 0,
          discountAmount: 0,
          originalPrice: item.price
        };

        // Apply discount only to services
        if (transactionItem.type === 'service' && discountPercentage && discountPercentage > 0) {
          const itemDiscountAmount = (item.price * discountPercentage) / 100;
          transactionItem.discountApplied = true;
          transactionItem.discountPercentage = discountPercentage;
          transactionItem.discountAmount = itemDiscountAmount;
          transactionItem.totalPrice = item.price - itemDiscountAmount;
        }

        items.push(transactionItem);

        if (transactionItem.type === 'service') {
          originalServiceAmount += item.price;
          serviceAmount += transactionItem.totalPrice;
        } else {
          productAmount += transactionItem.totalPrice;
        }
      });
    }

    // Calculate total amount
    const totalAmount = serviceAmount + productAmount;

    // Determine transaction type
    const transactionType = items.some(item => item.type === 'service') && items.some(item => item.type === 'product')
      ? TransactionType.CONSOLIDATED_SALE
      : items.some(item => item.type === 'service')
      ? TransactionType.SERVICE_SALE
      : TransactionType.PRODUCT_SALE;

    // Create description
    const serviceCount = items.filter(item => item.type === 'service').length;
    const productCount = items.filter(item => item.type === 'product').length;
    let description = '';
    
    if (serviceCount > 0 && productCount > 0) {
      description = `Booking payment - ${serviceCount} service(s) and ${productCount} product(s)`;
    } else if (serviceCount > 0) {
      description = `Booking payment - ${serviceCount} service(s)`;
    } else {
      description = `Booking payment - ${productCount} product(s)`;
    }

    // Add discount information to description
    if (discountPercentage && discountPercentage > 0) {
      description += ` (${discountPercentage}% discount applied to services)`;
    }

    const transaction: Transaction = {
      id: transactionId,
      date: new Date(),
      clientId: booking.clientId,
      clientName: booking.clientName,
      staffId: booking.staffId,
      staffName: booking.staffName,
      type: transactionType,
      category: transactionType === TransactionType.CONSOLIDATED_SALE ? "Consolidated Sale" : 
                transactionType === TransactionType.SERVICE_SALE ? "Service Sale" : "Product Sale",
      description,
      amount: totalAmount,
      paymentMethod,
      status: TransactionStatus.COMPLETED,
      location: booking.location || "loc1",
      source: TransactionSource.CALENDAR,
      reference: {
        type: "booking",
        id: booking.id
      },
      items,
      serviceAmount,
      productAmount,
      originalServiceAmount,
      discountPercentage,
      discountAmount,
      metadata: {
        bookingId: booking.id,
        transactionType: 'consolidated',
        serviceCount,
        productCount,
        discountApplied: discountPercentage && discountPercentage > 0,
        originalTotal: originalServiceAmount + productAmount,
        finalTotal: totalAmount
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    return transaction;
  }

  /**
   * Creates a consolidated transaction for POS sales
   * Applies discounts only to service items, not products
   */
  static createPOSTransaction(
    posData: any,
    paymentMethod: PaymentMethod,
    discountPercentage?: number,
    discountAmount?: number
  ): Transaction {
    const transactionId = `pos-tx-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const items: TransactionItem[] = [];

    let originalServiceAmount = 0;
    let serviceAmount = 0;
    let productAmount = 0;
    let serviceCount = 0;
    let productCount = 0;

    // Process cart items
    if (posData.items && posData.items.length > 0) {
      posData.items.forEach((item: any) => {
        const transactionItem: TransactionItem = {
          id: `${item.type}-${item.id}`,
          name: item.name,
          type: item.type,
          quantity: item.quantity,
          unitPrice: item.price,
          totalPrice: item.price * item.quantity,
          category: item.type === 'service' ? 'Service' : 'Product',
          sku: item.id,
          discountApplied: false
        };

        // Apply discount only to services
        if (item.type === 'service') {
          let itemDiscountAmount = 0;

          // Handle percentage discount
          if (discountPercentage && discountPercentage > 0) {
            itemDiscountAmount = (item.price * item.quantity * discountPercentage) / 100;
            transactionItem.discountApplied = true;
            transactionItem.discountPercentage = discountPercentage;
            transactionItem.discountAmount = itemDiscountAmount;
          }
          // Handle fixed discount amount (distributed proportionally across service items)
          else if (discountAmount && discountAmount > 0) {
            // Calculate this item's proportion of total service amount for proportional discount distribution
            const itemTotal = item.price * item.quantity;
            const totalServiceAmountForDiscount = posData.items
              .filter((i: any) => i.type === 'service')
              .reduce((sum: number, i: any) => sum + (i.price * i.quantity), 0);

            if (totalServiceAmountForDiscount > 0) {
              const itemProportion = itemTotal / totalServiceAmountForDiscount;
              itemDiscountAmount = discountAmount * itemProportion;
              transactionItem.discountApplied = true;
              transactionItem.discountAmount = itemDiscountAmount;
            }
          }

          // Apply the discount to the item's total price
          if (itemDiscountAmount > 0) {
            transactionItem.totalPrice = (item.price * item.quantity) - itemDiscountAmount;
          }
        }

        items.push(transactionItem);

        if (item.type === 'service') {
          originalServiceAmount += item.price * item.quantity;
          serviceAmount += transactionItem.totalPrice;
          serviceCount += item.quantity;
        } else {
          productAmount += transactionItem.totalPrice;
          productCount += item.quantity;
        }
      });
    }

    // Determine transaction type
    let transactionType = TransactionType.CONSOLIDATED_SALE;
    if (serviceCount > 0 && productCount === 0) {
      transactionType = TransactionType.SERVICE_SALE;
    } else if (productCount > 0 && serviceCount === 0) {
      transactionType = TransactionType.PRODUCT_SALE;
    }

    const totalAmount = serviceAmount + productAmount;

    // Create description
    let description = `POS Sale - ${items.length} item(s)`;
    if (serviceCount > 0 && productCount > 0) {
      description = `POS Sale - ${serviceCount} service(s), ${productCount} product(s)`;
    } else if (serviceCount > 0) {
      description = `POS Sale - ${serviceCount} service(s)`;
    } else {
      description = `POS Sale - ${productCount} product(s)`;
    }

    // Add discount information to description
    if (discountPercentage && discountPercentage > 0) {
      description += ` (${discountPercentage}% discount applied to services)`;
    }

    const transaction: Transaction = {
      id: transactionId,
      date: posData.date || new Date(),
      clientId: posData.clientId,
      clientName: posData.clientName || "Walk-in Customer",
      staffId: posData.staffId,
      staffName: posData.staffName || "Staff",
      type: transactionType,
      category: transactionType === TransactionType.CONSOLIDATED_SALE ? "Consolidated Sale" :
                transactionType === TransactionType.SERVICE_SALE ? "Service Sale" : "Product Sale",
      description,
      amount: totalAmount,
      paymentMethod,
      status: TransactionStatus.COMPLETED,
      location: posData.location || "loc1",
      source: TransactionSource.POS,
      reference: {
        type: "pos_sale",
        id: posData.id
      },
      items,
      serviceAmount,
      productAmount,
      originalServiceAmount,
      discountPercentage,
      discountAmount,
      metadata: {
        posTransactionId: posData.id,
        transactionType: 'consolidated',
        serviceCount,
        productCount,
        discountApplied: (discountPercentage && discountPercentage > 0) || (discountAmount && discountAmount > 0),
        discountAppliedTo: 'services_only',
        originalTotal: originalServiceAmount + productAmount,
        finalTotal: totalAmount,
        source: 'POS',
        ...posData.metadata
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    return transaction;
  }
}
